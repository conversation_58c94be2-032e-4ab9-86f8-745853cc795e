using System.Text.Json;
using YseStore.IService.Visual;
using YseStore.Model.Visual;
using YseStore.Model.Response;
using YseStore.IService.Base;
using Microsoft.Extensions.Logging;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 可视化页面服务实现
    /// </summary>
    public class VisualPageService : IVisualPageService
    {
        private readonly ILogger<VisualPageService> _logger;
        private readonly IBaseService _baseService;

        public VisualPageService(ILogger<VisualPageService> logger, IBaseService baseService)
        {
            _logger = logger;
            _baseService = baseService;
        }

        /// <summary>
        /// 获取可视化页面数据
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>可视化页面数据</returns>
        public async Task<ApiResponse<VisualPageDataResponse>> GetVisualPageDataAsync(VisualPageDataRequest request)
        {
            try
            {
                _logger.LogInformation("开始获取可视化页面数据，PageType: {PageType}", request.PageType);

                // 模拟数据 - 实际应该从数据库获取
                var visualPageData = new VisualPageDataResponse
                {
                    PluginsByType = new Dictionary<string, List<VisualPagePlugin>>
                    {
                        ["header"] = new List<VisualPagePlugin>
                        {
                            new VisualPagePlugin
                            {
                                PId = 21282,
                                Type = "header",
                                Mode = "mode_73",
                                Settings = "{\"Search\":\"1\",\"SearchPlaceholder\":\"Search by keyword...\",\"User\":\"1\",\"ShoppingCart\":\"1\",\"HeaderFixedPc\":\"0\",\"HeaderFixedMobile\":\"0\",\"LanguageSwitch\":\"1\"}",
                                Blocks = "{\"Logo\":{\"Logo\":\"/u_file/2507/09/photo/u36795792273805816557fm253app138fJPEG-94f8.jpg\",\"ImageAlt\":\"RETEVS\",\"LogoWidth\":\"240px\",\"MobileLogoWidth\":\"162px\"},\"Menu\":{\"Menu\":\"1\"}}",
                                Config = "{\"Display\":\"1\"}",
                                // 预解析Settings为对象，避免前端解析问题
                                ParsedSettings = new Dictionary<string, object>
                                {
                                    ["Search"] = "1",
                                    ["SearchPlaceholder"] = "Search by keyword...",
                                    ["User"] = "1",
                                    ["ShoppingCart"] = "1",
                                    ["HeaderFixedPc"] = "0",
                                    ["HeaderFixedMobile"] = "0",
                                    ["LanguageSwitch"] = "1"
                                }
                            }
                        }
                    }
                };

                _logger.LogInformation("可视化页面数据获取成功");

                return new ApiResponse<VisualPageDataResponse>
                {
                    Success = true,
                    Data = visualPageData,
                    Message = "获取成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可视化页面数据失败");
                return new ApiResponse<VisualPageDataResponse>
                {
                    Success = false,
                    Message = "获取可视化页面数据失败",
                    Data = new VisualPageDataResponse { PluginsByType = new Dictionary<string, List<VisualPagePlugin>>() }
                };
            }
        }

        /// <summary>
        /// 安全序列化可视化页面数据为JSON
        /// </summary>
        /// <param name="data">可视化页面数据</param>
        /// <returns>JSON字符串</returns>
        public string SerializeVisualPageDataSafely(VisualPageDataResponse data)
        {
            try
            {
                if (data == null)
                {
                    return "{}";
                }

                // 处理Settings字段，确保JSON序列化正确
                foreach (var pluginType in data.PluginsByType)
                {
                    foreach (var plugin in pluginType.Value)
                    {
                        // 如果Settings是JSON字符串，尝试解析为对象
                        if (!string.IsNullOrEmpty(plugin.Settings) && plugin.ParsedSettings == null)
                        {
                            try
                            {
                                var settingsDict = JsonSerializer.Deserialize<Dictionary<string, object>>(plugin.Settings);
                                plugin.ParsedSettings = settingsDict;
                            }
                            catch (JsonException ex)
                            {
                                _logger.LogWarning(ex, "解析Settings JSON失败，PId: {PId}", plugin.PId);
                                plugin.ParsedSettings = new Dictionary<string, object>();
                            }
                        }
                    }
                }

                // 使用JsonSerializer进行安全序列化
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                return JsonSerializer.Serialize(data, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "序列化可视化页面数据失败");
                return "{}";
            }
        }
    }
}
