using YseStore.Model.Visual;
using YseStore.Model.Response;

namespace YseStore.IService.Visual
{
    /// <summary>
    /// 可视化页面服务接口
    /// </summary>
    public interface IVisualPageService
    {
        /// <summary>
        /// 获取可视化页面数据
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>可视化页面数据</returns>
        Task<ApiResponse<VisualPageDataResponse>> GetVisualPageDataAsync(VisualPageDataRequest request);

        /// <summary>
        /// 安全序列化可视化页面数据为JSON
        /// </summary>
        /// <param name="data">可视化页面数据</param>
        /// <returns>JSON字符串</returns>
        string SerializeVisualPageDataSafely(VisualPageDataResponse data);
    }
}
